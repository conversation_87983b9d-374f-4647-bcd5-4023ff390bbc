// Learn more about <PERSON>ri commands at https://tauri.app/develop/calling-rust/
use btleplug::api::{Central, CentralEvent, Manager as _, Peripheral as _, ScanFilter};
use btleplug::platform::{<PERSON><PERSON><PERSON>, Manager, Peripheral};
use futures_util::StreamExt;
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Emitter, Manager as _, State};
use tokio::sync::broadcast;

#[derive(Debug, serde::Deserialize)]
struct StartScanArgs {
    #[serde(alias = "manufacturerId", alias = "manufacturer_id")]
    manufacturer_id: Option<u16>,
}

fn build_device_from_properties(
    id: &btleplug::platform::PeripheralId,
    properties: &btleplug::api::PeripheralProperties,
) -> BleDevice {
    let address = id.to_string();
    let rssi = properties.rssi.unwrap_or(0);
    let manufacturer_data = properties
        .manufacturer_data
        .iter()
        .map(|(k, v)| (k.to_string(), v.clone()))
        .collect();
    let name = properties
        .local_name
        .clone()
        .unwrap_or_else(|| address.clone());

    let services: Vec<String> = properties.services.iter().map(|u| u.to_string()).collect();

    BleDevice {
        name,
        address,
        rssi,
        is_connected: false,
        manufacturer_data,
        services,
    }
}

// 定义设备信息结构体
#[derive(Debug, Clone, serde::Serialize)]
pub struct BleDevice {
    pub name: String,
    pub address: String,
    pub rssi: i16,
    pub is_connected: bool,
    pub manufacturer_data: HashMap<String, Vec<u8>>,
    pub services: Vec<String>,
}

#[derive(Debug, Clone, serde::Serialize)]
struct DeviceServicesUpdate {
    address: String,
    services: Vec<String>,
}

// 定义应用状态
struct AppState {
    central: Option<Adapter>,
    peripherals: HashMap<String, Peripheral>,
    scanning_tx: Option<broadcast::Sender<Vec<BleDevice>>>,
    connection_tx: Option<broadcast::Sender<bool>>,
    // 缓存广播中宣告的服务（尤其用于 macOS 上 HID 被隐藏场景做兜底）
    advertised_services: HashMap<String, Vec<String>>,
}

impl AppState {
    fn new() -> Self {
        Self {
            central: None,
            peripherals: HashMap::new(),
            scanning_tx: None,
            connection_tx: None,
            advertised_services: HashMap::new(),
        }
    }
}

#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

// 获取蓝牙适配器
#[tauri::command]
async fn get_adapter() -> Result<(), String> {
    let manager = Manager::new().await.map_err(|e| e.to_string())?;
    let adapters = manager.adapters().await.map_err(|e| e.to_string())?;
    if adapters.is_empty() {
        return Err("No Bluetooth adapters found".to_string());
    }
    Ok(())
}

// 开始扫描设备
#[tauri::command]
async fn start_scan(
    app_handle: AppHandle,
    state: State<'_, Arc<Mutex<AppState>>>,
    args: Option<StartScanArgs>,
) -> Result<(), String> {
    let manufacturer_id = args.and_then(|a| a.manufacturer_id);
    let manager = Manager::new().await.map_err(|e| e.to_string())?;
    let adapters = manager.adapters().await.map_err(|e| e.to_string())?;
    let central = adapters
        .into_iter()
        .next()
        .ok_or("No Bluetooth adapters found".to_string())?;

    let (tx, _) = broadcast::channel(16);
    let tx_clone = tx.clone();

    {
        let mut state = state.lock().unwrap();
        state.central = Some(central.clone());
        state.scanning_tx = Some(tx);
        state.peripherals.clear();
    }

    // 监听蓝牙事件
    let mut events = central.events().await.map_err(|e| e.to_string())?;
    let central_clone = central.clone();
    let app_handle_clone = app_handle.clone();
    let vendor_filter = manufacturer_id;

    // 启动扫描任务
    tauri::async_runtime::spawn(async move {
        // 清除之前的扫描结果
        let mut device_map: HashMap<String, BleDevice> = HashMap::new();

        // 统一处理属性 → 设备、过滤、发送与广告服务缓存的辅助函数
        fn handle_properties(
            id: &btleplug::platform::PeripheralId,
            properties: &btleplug::api::PeripheralProperties,
            vendor_filter: Option<u16>,
            app_handle: &AppHandle,
            device_map: &mut HashMap<String, BleDevice>,
            tx: &broadcast::Sender<Vec<BleDevice>>,
        ) {
            // 按可选的厂商 ID 过滤
            if let Some(filter_id) = vendor_filter {
                if properties.manufacturer_data.is_empty() {
                    return;
                }
                if !properties.manufacturer_data.contains_key(&filter_id) {
                    return;
                }
            }

            let address = id.to_string();
            let mut device = build_device_from_properties(id, properties);

            // 合并此前缓存/已推送的服务，避免连接后广播服务丢失
            // 1) 若当前服务为空且 device_map 里已有旧的非空服务，沿用旧服务
            if device.services.is_empty() {
                if let Some(prev) = device_map.get(&address) {
                    if !prev.services.is_empty() {
                        device.services = prev.services.clone();
                    }
                }
            }
            // 2) 若仍为空，尝试从 advertised_services 兜底
            if device.services.is_empty() {
                let state_guard = app_handle.state::<Arc<Mutex<AppState>>>();
                let state = state_guard.lock().unwrap();
                if let Some(adv) = state.advertised_services.get(&address) {
                    device.services = adv.clone();
                }
                drop(state);
            }
            // 3) 若两边都有数据，做并集去重
            else {
                let state_guard = app_handle.state::<Arc<Mutex<AppState>>>();
                let state = state_guard.lock().unwrap();
                if let Some(adv) = state.advertised_services.get(&address) {
                    if !adv.is_empty() {
                        let mut merged = device.services.clone();
                        merged.extend(adv.iter().cloned());
                        merged.sort();
                        merged.dedup();
                        device.services = merged;
                    }
                }
                drop(state);
            }

            // 缓存广播宣告的服务，供 macOS 兜底
            if !device.services.is_empty() {
                let state_guard = app_handle.state::<Arc<Mutex<AppState>>>();
                let mut state = state_guard.lock().unwrap();
                state
                    .advertised_services
                    .insert(address.clone(), device.services.clone());
            }

            device_map.insert(address.clone(), device.clone());
            let devices: Vec<BleDevice> = device_map.values().cloned().collect();
            let _ = tx.send(devices);
        }

        // 开始扫描
        if let Err(err) = central_clone.start_scan(ScanFilter::default()).await {
            eprintln!("Error starting scan: {:?}", err);
            return;
        }

        // 处理扫描到的设备
        while let Some(event) = events.next().await {
            match event {
                CentralEvent::DeviceDiscovered(id) => {
                    if let Ok(peripheral) = central_clone.peripheral(&id).await {
                        if let Ok(Some(properties)) = peripheral.properties().await {
                            // 先保存外设引用，便于后续 DeviceUpdated 使用
                            let address = id.to_string();
                            let state_guard = app_handle_clone.state::<Arc<Mutex<AppState>>>();
                            let mut state = state_guard.lock().unwrap();
                            state.peripherals.insert(address, peripheral);
                            drop(state);

                            handle_properties(
                                &id,
                                &properties,
                                vendor_filter,
                                &app_handle_clone,
                                &mut device_map,
                                &tx_clone,
                            );
                        }
                    }
                }
                CentralEvent::DeviceUpdated(id) => {
                    if let Ok(peripheral) = central_clone.peripheral(&id).await {
                        if let Ok(Some(properties)) = peripheral.properties().await {
                            handle_properties(
                                &id,
                                &properties,
                                vendor_filter,
                                &app_handle_clone,
                                &mut device_map,
                                &tx_clone,
                            );
                        }
                    }
                }
                _ => {}
            }
        }
    });

    Ok(())
}

// 停止扫描
#[tauri::command]
async fn stop_scan(state: State<'_, Arc<Mutex<AppState>>>) -> Result<(), String> {
    let central = {
        let state = state.lock().unwrap();
        state.central.clone()
    };

    if let Some(central) = central {
        central.stop_scan().await.map_err(|e| e.to_string())?;
    }
    Ok(())
}

// 连接设备
#[tauri::command]
async fn connect_device(
    app_handle: AppHandle,
    address: String,
    state: State<'_, Arc<Mutex<AppState>>>,
) -> Result<(), String> {
    let (peripheral, connection_tx) = {
        let state = state.lock().unwrap();
        let peripheral = state.peripherals.get(&address).cloned();
        let connection_tx = state.connection_tx.clone();
        (peripheral, connection_tx)
    };

    if let Some(peripheral) = peripheral {
        // 连接设备
        if let Err(err) = peripheral.connect().await {
            return Err(err.to_string());
        }

        // 发现服务
        if let Err(err) = peripheral.discover_services().await {
            return Err(err.to_string());
        }

        // 合并当前服务与广播缓存服务，向前端推送一次更新
        let mut merged: Vec<String> = peripheral
            .services()
            .iter()
            .map(|s| s.uuid.to_string())
            .collect();
        {
            let state_locked = state.lock().unwrap();
            if let Some(adv) = state_locked.advertised_services.get(&address) {
                if !adv.is_empty() {
                    merged.extend(adv.clone());
                }
            }
        }
        merged.sort();
        merged.dedup();

        // 发送连接状态更新
        if let Some(tx) = &connection_tx {
            let _ = tx.send(true);
        }

        return Ok(());
    }
    Err("Device not found".to_string())
}

// 断开连接
#[tauri::command]
async fn disconnect_device(
    address: String,
    state: State<'_, Arc<Mutex<AppState>>>,
) -> Result<(), String> {
    let (peripheral, connection_tx) = {
        let state = state.lock().unwrap();
        let peripheral = state.peripherals.get(&address).cloned();
        let connection_tx = state.connection_tx.clone();
        (peripheral, connection_tx)
    };

    if let Some(peripheral) = peripheral {
        if let Err(err) = peripheral.disconnect().await {
            return Err(err.to_string());
        }

        // 发送连接状态更新
        if let Some(tx) = &connection_tx {
            let _ = tx.send(false);
        }

        return Ok(());
    }
    Err("Device not found".to_string())
}

// 获取设备服务
#[tauri::command]
async fn get_device_services(
    address: String,
    state: State<'_, Arc<Mutex<AppState>>>,
) -> Result<Vec<String>, String> {
    let peripheral = {
        let state = state.lock().unwrap();
        state.peripherals.get(&address).cloned()
    };

    if let Some(peripheral) = peripheral {
        // Windows: 一般可直接读取到 HID(0x1812)。
        // macOS: CoreBluetooth 可能隐藏部分 GATT（如 HID）。
        // 兜底策略：
        // 1) 优先取已发现服务
        // 2) 若为空，尝试合并扫描期广播宣告的服务（在设备发现阶段已缓存）

        let mut merged: Vec<String> = peripheral
            .services()
            .iter()
            .map(|service| service.uuid.to_string())
            .collect();

        if merged.is_empty() {
            let adv = {
                let state = state.lock().unwrap();
                state
                    .advertised_services
                    .get(&address)
                    .cloned()
                    .unwrap_or_default()
            };
            if !adv.is_empty() {
                merged.extend(adv);
            }
        }

        // 去重
        merged.sort();
        merged.dedup();
        return Ok(merged);
    }
    Err("Failed to get services".to_string())
}

// 监听扫描更新 - 通过事件系统发送
#[tauri::command]
async fn start_scan_listener(app_handle: AppHandle) -> Result<(), String> {
    let state_guard = app_handle.state::<Arc<Mutex<AppState>>>();

    {
        let state = state_guard.lock().unwrap();
        if let Some(scanning_tx) = &state.scanning_tx {
            let mut rx_clone = scanning_tx.subscribe();
            let app_handle_clone = app_handle.clone();

            // 启动监听任务
            tauri::async_runtime::spawn(async move {
                while let Ok(devices) = rx_clone.recv().await {
                    let _ = app_handle_clone.emit("scan-update", devices);
                }
            });
        }
    }

    Ok(())
}

// 监听连接更新 - 通过事件系统发送
#[tauri::command]
async fn start_connection_listener(app_handle: AppHandle) -> Result<(), String> {
    let (broadcast_tx, _) = broadcast::channel(16);

    let state_guard = app_handle.state::<Arc<Mutex<AppState>>>();
    {
        let mut state = state_guard.lock().unwrap();
        state.connection_tx = Some(broadcast_tx.clone());
    }

    let mut broadcast_rx = broadcast_tx.subscribe();
    let app_handle_clone = app_handle.clone();

    // 启动监听任务
    tauri::async_runtime::spawn(async move {
        while let Ok(connected) = broadcast_rx.recv().await {
            let _ = app_handle_clone.emit("connection-update", connected);
        }
    });

    Ok(())
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    let app_state = Arc::new(Mutex::new(AppState::new()));

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .manage(app_state)
        .invoke_handler(tauri::generate_handler![
            greet,
            get_adapter,
            start_scan,
            stop_scan,
            connect_device,
            disconnect_device,
            get_device_services,
            start_scan_listener,
            start_connection_listener
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
