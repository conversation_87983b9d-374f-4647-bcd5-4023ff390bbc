<template>
  <div class="flex flex-col h-screen bg-white rounded-2xl shadow-md">
    <!-- Header -->
    <div class="flex justify-between items-center p-4 border-b">
      <h1 class="flex items-center gap-2 text-lg font-semibold">
        <span class="icon-[mdi--bluetooth-audio] text-2xl text-blue-500" />
        <span class="text-blue-500">蓝牙管理</span>
      </h1>
      <div class="flex items-center gap-3">
        <v-btn circle icon="mdi-cog-outline" size="large" />
        <v-btn circle icon="mdi-refresh" size="large" />

        <v-switch :model-value="scanning" color="primary" :label="scanning ? '停止搜索' : '开始搜索'" @update:modelValue="handleUpdateSwitch" />
      </div>
    </div>

    <div class="flex justify-between items-center p-4 border-b text-red-500">
      <h2 class="font-semibold text-red-500">已连接设备</h2>
    </div>

    <!-- Body -->
    <div class="flex flex-1 divide-x">
      <!-- Nearby Devices -->
      <div class="w-1/2 p-4">
        <h2 class="font-semibold mb-3">附近设备</h2>
        <div class="space-y-3 h-[calc(100dvh-200px)] overflow-y-auto">
          <template v-for="(bleDevice, key) in deviceList" :key="key">
            <div class="p-3 border rounded-xl hover:bg-gray-50" @click="chooseDevice = bleDevice">
              <div class="flex justify-between items-center">
                <p class="font-medium">{{ bleDevice.name }}</p>
                <p v-if="bleDevice.isConnected" class="text-green-600 text-sm">•已连接</p>
              </div>
              <div class="flex gap-6">
                <p class="w-1/2 overflow-hidden">
                  <span class="30">&nbsp;&nbsp;{{ bleDevice.address }}</span>
                </p>
                <p class="w-1/2 text-sm">信号强度: {{ bleDevice.rssi }}</p>
              </div>
              <button v-if="bleDevice.isConnected" class="px-3 py-1 border rounded-lg hover:bg-gray-100"
                @click="handleDisconnectDevice()">断开连接</button>
            </div>
          </template>
        </div>
      </div>

      <!-- Device Details -->
      <div class="w-1/2 p-4">
        <h2 class="font-semibold mb-3">设备详情</h2>
        <div v-if="chooseDevice" class="p-4 border rounded-xl">
          <p><span class="font-medium">设备名称:</span> {{ chooseDevice?.name }}</p>
          <p><span class="font-medium">状态:</span> {{ chooseDevice?.isConnected ? '已连接' : '未连接' }}</p>
          <p><span class="font-medium">电量:</span> 80%</p>
          <p><span class="font-medium">服务:</span>
          </p>
          <p v-for="(service, key) in chooseDevice?.services" :key="key">{{ service }}</p>
          <div class="flex gap-2 mt-4">
            <button class="px-3 py-1 border rounded-lg hover:bg-gray-100">断开连接</button>
            <button class="px-3 py-1 border rounded-lg hover:bg-gray-100">忽略设备</button>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div class="flex justify-between items-center p-3 border-t text-sm text-gray-600">
      <span>Adapter: Intel Wireless Bluetooth v5.3</span>
      <span>Connected: 2 devices</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { type BleDevice, connect, disconnect, getConnectionUpdates, getScanningUpdates, readString, sendString, startScan, stopScan, subscribeString, unsubscribe } from '@mnlphlp/plugin-blec'
import { onMounted, onUnmounted, ref } from 'vue'

const deviceList = ref<BleDevice[]>([])
const connected = ref(false)
const scanning = ref(false)

onMounted(async () => {
  await getConnectionUpdates(state => connected.value = state)
  await getScanningUpdates(state => {

    scanning.value = state
  })
})

const ab2hex = (buffer: ArrayBuffer): string => {
  const hexArr = Array.prototype.map.call(
    new Uint8Array(buffer),
    function (bit: number) {
      return ("00" + bit.toString(16)).slice(-2);
    }
  );
  return hexArr.join("");
};

const chooseDevice = ref<BleDevice>()

// const SERVICE_UUID = 'A07498CA-AD5B-474E-940D-16F1FBE7E8CD'
const CHARACTERISTIC_UUID = '51FF12BB-3ED8-46E5-B4F9-D64E2FEC021B'

async function handleUpdateSwitch(value: any) {
  console.log('蓝牙状态 value', value)
  scanning.value = value

  if (value) {
    deviceList.value = []
    await startScan((devices: BleDevice[]) => {
      devices.filter((device: BleDevice) => {
        if (device.name == "MB0102") {
          console.log('设备信息', device, device.manufacturerData)
        }

        if (device.manufacturerData == 0xe208) {

        }
      
        // const company = ab2hex(device?.name?.slice(0, 2));
        // console.log("设备名称", device?.name)
      })
      

      deviceList.value = devices

      return
    }, 10_000)
  } else {
    await stopScan()
  }

  return
}

function handleDisconnectDevice() {
  disconnect()
}

const notifyData = ref('')
async function subscribe() {
  if (notifyData.value) {
    unsubscribe(CHARACTERISTIC_UUID)
    notifyData.value = ''
  } else {
    await subscribeString(CHARACTERISTIC_UUID, (data: string) => notifyData.value = data)
  }
}

onUnmounted(() => {
  // 停止扫描
  stopScan()
  // 取消订阅特征值
  if (notifyData.value) {
    unsubscribe(CHARACTERISTIC_UUID)
  }
  // 注意：有些Tauri插件可能需要特定的方法来清理所有回调
  // 如果plugin-blec提供了清理方法，也应该在这里调用
})
</script>

<style scoped></style>
