{"name": "ble", "private": true, "type": "module", "version": "0.0.0", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --fix", "tauri": "tauri"}, "dependencies": {"@fontsource/roboto": "5.2.7", "@mdi/font": "7.4.47", "@mnlphlp/plugin-blec": "^0.7.0", "@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "pinia": "^3.0.3", "vue": "^3.5.21", "vue-router": "^4.5.1", "vuetify": "^3.10.1"}, "devDependencies": {"@tsconfig/node22": "^22.0.0", "@types/node": "^22.9.0", "@vitejs/plugin-vue": "^6.0.1", "@vue/tsconfig": "^0.8.1", "@tauri-apps/cli": "^2", "eslint": "^9.35.0", "eslint-config-vuetify": "^4.2.0", "npm-run-all2": "^8.0.4", "sass-embedded": "^1.92.1", "typescript": "~5.9.2", "unplugin-auto-import": "^20.1.0", "unplugin-fonts": "^1.4.0", "unplugin-vue-components": "^29.0.0", "unplugin-vue-router": "^0.15.0", "vite": "^7.1.5", "vite-plugin-vue-layouts-next": "^1.0.0", "vite-plugin-vuetify": "^2.1.2", "vue-tsc": "^3.0.7"}}